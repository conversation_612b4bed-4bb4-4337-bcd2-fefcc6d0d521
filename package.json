{"name": "workers-getting-started", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "test": "vitest", "cf-typegen": "wrangler types"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.19", "@playwright/test": "^1.53.2", "typescript": "^5.5.2", "vitest": "~3.2.0", "wrangler": "^4.23.0"}, "dependencies": {"hono": "^4.8.4"}}